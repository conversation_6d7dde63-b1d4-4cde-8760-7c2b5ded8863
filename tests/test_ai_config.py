"""
测试AI配置功能
"""
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from api_key_config import APIKeyConfigDialog, get_saved_config
from analysis_engine import AnalysisEngine
import tkinter as tk

def test_api_config_dialog():
    """测试API配置对话框"""
    print("🤖 测试AI配置对话框")
    print("=" * 50)
    
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    
    print("打开AI配置对话框...")
    dialog = APIKeyConfigDialog()
    result = dialog.show()
    
    if result:
        print("✅ 配置成功:")
        print(f"  模型类型: {result['model_type']}")
        print(f"  API Key: {result['api_key'][:10]}..." if result['api_key'] else "  API Key: 未设置")
    else:
        print("❌ 用户取消了配置")
    
    root.destroy()
    return result

def test_ai_analysis(config):
    """测试AI分析功能"""
    if not config:
        print("⚠️ 没有AI配置，跳过AI分析测试")
        return
    
    print("\n🔍 测试AI分析功能")
    print("=" * 50)
    
    try:
        # 创建带AI配置的分析引擎
        api_key = config.get('api_key')
        model_type = config.get('model_type', 'gemini')
        
        print(f"使用 {model_type} 模型初始化分析引擎...")
        engine = AnalysisEngine(ai_api_key=api_key, ai_model_type=model_type)
        
        # 测试文档
        available_docs = [
            "SYS-050-AHT RISD系统需求规范-V0.4-20250725.docx",
            "SYS-050-T9M-SIDP系统需求规范-V1.3-20250716.docx"
        ]
        
        selected_doc = None
        for doc in available_docs:
            if os.path.exists(doc):
                selected_doc = doc
                break
        
        if not selected_doc:
            print("❌ 未找到测试文档")
            return
        
        print(f"📄 分析文档: {selected_doc}")
        print("🔄 开始AI分析...")
        
        # 执行分析
        result = engine.analyze_document(selected_doc)
        
        print("✅ AI分析完成!")
        print(f"📊 分析结果:")
        print(f"  • 总问题数: {len(result.issues)}")
        print(f"  • 评审问题: {len(result.review_questions) if result.review_questions else 0}")
        print(f"  • AI洞察: {len(result.ai_insights.get('test_risks', []))}")
        
        # 显示AI洞察示例
        ai_insights = result.ai_insights
        if ai_insights and ai_insights.get('test_risks'):
            print(f"\n🤖 AI洞察示例:")
            for i, risk in enumerate(ai_insights['test_risks'][:3], 1):
                print(f"  {i}. {risk}")
        
        print(f"\n📈 总体评分: {result.overall_score:.1f}/100")
        
    except Exception as e:
        print(f"❌ AI分析测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_saved_config():
    """测试已保存的配置"""
    print("\n💾 测试已保存的配置")
    print("=" * 50)
    
    config = get_saved_config()
    if config:
        print("✅ 找到已保存的配置:")
        print(f"  模型类型: {config['model_type']}")
        print(f"  API Key: {'已设置' if config.get('api_key') else '未设置'}")
        return config
    else:
        print("❌ 未找到已保存的配置")
        return None

def main():
    """主函数"""
    print("🚀 AI配置功能测试")
    print("=" * 80)
    
    # 测试已保存的配置
    saved_config = test_saved_config()
    
    # 如果没有保存的配置，打开配置对话框
    if not saved_config:
        print("\n需要配置AI模型...")
        config = test_api_config_dialog()
    else:
        config = saved_config
        print(f"\n使用已保存的配置进行测试...")
    
    # 测试AI分析
    if config:
        test_ai_analysis(config)
    
    print("\n" + "=" * 80)
    print("🎉 AI配置功能测试完成!")
    
    if config:
        print("\n💡 使用建议:")
        print("  1. 现在可以启动GUI界面体验AI分析功能")
        print("  2. 在GUI中点击'🤖 AI配置'可以修改设置")
        print("  3. AI分析会提供更深度的需求洞察")
        print("  4. 分析速度会比基础模式慢一些（10-30秒）")
    else:
        print("\n💡 提示:")
        print("  1. 需要先配置AI模型才能使用AI分析功能")
        print("  2. 支持Google Gemini和OpenAI GPT模型")
        print("  3. 没有AI配置时会使用基础分析模式")

if __name__ == "__main__":
    main()
