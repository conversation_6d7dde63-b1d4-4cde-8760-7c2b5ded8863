"""
测试需求评审问题生成功能
"""
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from analysis_engine import AnalysisEngine
from requirement_reviewer import RequirementReviewer
from report_generator import ReportGenerator
from document_parser import DocumentParser, DocumentMetadata

def test_review_questions():
    """测试评审问题生成功能"""
    print("❓ 测试需求评审问题生成功能...")
    print("=" * 60)
    
    # 创建测试用的需求文档内容
    test_content = """
    汽车智能交互显示系统功能需求
    
    1. 闭锁功能
    当用户执行闭锁操作时，系统播放闭锁灯语动画。播放结束后，系统需要进入某种状态。
    
    2. 迎宾功能  
    迎宾功能在特定条件下触发。迎宾功能结束后，如果收到交互灯语和指示灯语的请求，
    系统需要进行相应处理。
    
    3. 充电显示功能
    充电显示功能显示当前电量百分比。当充电到100%时，系统行为待定。
    电量显示可能存在颗粒度问题，95%电量的显示方式需要确认。
    
    4. 灯语优先级
    交互灯语和指示灯语可能同时触发。充电指示和临停灯语也可能冲突。
    
    5. 信号定义
    系统使用EL_WelcomeLightSt信号，但该信号的具体作用需要明确。
    临停指示功能的信号接口需要定义。
    
    6. 通讯处理
    当单边屏幕出现通讯丢失时，系统的处理策略需要明确。
    
    7. 故障处理
    故障的触发和恢复时间需要定义。
    
    8. 自定义灯语
    自定义灯语的字符支持和长度限制需要明确。
    
    9. 诊断功能
    诊断命令与正常功能的交互逻辑需要明确。
    """
    
    # 模拟解析数据
    parsed_data = {
        'raw_text': test_content,
        'requirements': [],
        'metadata': None
    }
    
    # 测试需求评审问题生成器
    print("🔍 测试需求评审问题生成器...")
    reviewer = RequirementReviewer()
    questions = reviewer.generate_review_questions(parsed_data)
    
    print(f"✅ 生成了 {len(questions)} 个评审问题")
    print()
    
    # 按优先级分组显示
    high_priority = [q for q in questions if q.priority == "HIGH"]
    medium_priority = [q for q in questions if q.priority == "MEDIUM"]
    low_priority = [q for q in questions if q.priority == "LOW"]
    
    if high_priority:
        print("🚨 高优先级问题:")
        for i, q in enumerate(high_priority, 1):
            print(f"{i}. [{q.category}] {q.question}")
            print(f"   担忧: {q.concern}")
            print(f"   影响: {q.impact}")
            print(f"   建议: {q.suggestion}")
            print()
    
    if medium_priority:
        print("⚠️ 中优先级问题:")
        for i, q in enumerate(medium_priority, 1):
            print(f"{i}. [{q.category}] {q.question}")
            print(f"   担忧: {q.concern}")
            print()
    
    if low_priority:
        print("💡 低优先级问题:")
        for i, q in enumerate(low_priority, 1):
            print(f"{i}. [{q.category}] {q.question}")
            print()
    
    # 生成评审报告
    print("📝 生成评审报告...")
    report = reviewer.generate_review_report(questions)
    
    # 保存评审报告
    report_path = "需求评审问题清单.md"
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(report)
    print(f"✅ 评审报告已保存: {report_path}")
    print()
    
    return questions

def test_integrated_analysis():
    """测试集成分析功能"""
    print("🔧 测试集成分析功能...")
    print("=" * 60)
    
    # 选择文档进行分析
    available_docs = [
        "SYS-050-AHT RISD系统需求规范-V0.4-20250725.docx",
        "SYS-050-T9M-SIDP系统需求规范-V1.3-20250716.docx"
    ]
    
    selected_doc = None
    for doc in available_docs:
        if os.path.exists(doc):
            selected_doc = doc
            break
    
    if not selected_doc:
        print("❌ 未找到可用的需求文档")
        return
    
    print(f"📄 分析文档: {selected_doc}")
    
    # 执行完整分析
    engine = AnalysisEngine()
    analysis_result = engine.analyze_document(selected_doc)
    
    print(f"📈 总体评分: {analysis_result.overall_score:.1f}/100")
    print(f"📋 发现问题总数: {len(analysis_result.issues)}")
    
    # 显示评审问题
    if analysis_result.review_questions:
        print(f"❓ 生成评审问题: {len(analysis_result.review_questions)} 个")
        print("\n评审问题预览:")
        for i, question in enumerate(analysis_result.review_questions[:5], 1):
            print(f"{i}. {question}")
        
        if len(analysis_result.review_questions) > 5:
            print(f"... 还有 {len(analysis_result.review_questions) - 5} 个问题")
    else:
        print("❓ 未生成评审问题")
    
    print()
    
    # 生成完整报告
    print("📝 生成完整分析报告...")
    report_generator = ReportGenerator()
    
    # 解析文档元数据
    parser = DocumentParser()
    parsed_data = parser.parse_document(selected_doc)
    metadata = parsed_data.get('metadata')
    
    if not metadata:
        metadata = DocumentMetadata(
            title=os.path.basename(selected_doc),
            version="未知版本",
            author="未知作者",
            total_words=0,
            total_requirements=len(parsed_data.get('requirements', []))
        )
    
    report_path = report_generator.generate_report(analysis_result, metadata, "html")
    print(f"✅ 完整报告已生成: {report_path}")
    
    return analysis_result

def demonstrate_review_focus():
    """演示评审重点功能"""
    print("🎯 演示评审重点功能...")
    print("=" * 60)
    
    print("新的分析重点:")
    focus_areas = [
        "🔍 深度挖掘条件逻辑漏洞",
        "❓ 识别模糊不清的需求表述", 
        "⚠️ 发现遗漏的边界条件",
        "🚨 提出关键担忧和风险点",
        "💡 生成具体的评审问题",
        "📋 提供评审会议讨论清单"
    ]
    
    for area in focus_areas:
        print(f"  {area}")
    
    print()
    print("评审问题类型:")
    question_types = [
        "状态转换: 功能结束后的状态定义",
        "触发条件: 条件组合和边界情况", 
        "功能优先级: 冲突处理和排队机制",
        "信号接口: 信号定义和通讯故障",
        "时序逻辑: 响应时间和时序同步",
        "边界条件: 数值边界和字符限制",
        "用户场景: 异常操作和极端环境"
    ]
    
    for qtype in question_types:
        print(f"  • {qtype}")
    
    print()
    print("💼 这些问题可以直接用于需求评审会议，帮助团队:")
    benefits = [
        "提前发现需求中的逻辑漏洞和模糊点",
        "避免开发过程中的理解偏差",
        "减少后期返工和bug修复成本",
        "提高需求文档的质量和完整性",
        "为测试用例设计提供重点方向"
    ]
    
    for benefit in benefits:
        print(f"  ✓ {benefit}")

def main():
    """主函数"""
    print("🚀 需求评审问题生成功能测试")
    print("=" * 80)
    
    # 演示评审重点
    demonstrate_review_focus()
    print()
    
    # 测试评审问题生成
    questions = test_review_questions()
    print()
    
    # 测试集成分析
    result = test_integrated_analysis()
    print()
    
    print("🎉 测试完成!")
    print("=" * 80)
    
    # 总结
    print("📊 测试结果总结:")
    print(f"  • 生成评审问题: {len(questions) if questions else 0} 个")
    print(f"  • 分析问题总数: {len(result.issues) if result else 0} 个")
    print(f"  • 评审问题数量: {len(result.review_questions) if result and result.review_questions else 0} 个")
    print()
    print("💡 建议:")
    print("  1. 查看生成的'需求评审问题清单.md'文件")
    print("  2. 打开HTML报告查看完整的评审问题部分")
    print("  3. 将评审问题用于实际的需求评审会议")

if __name__ == "__main__":
    main()
