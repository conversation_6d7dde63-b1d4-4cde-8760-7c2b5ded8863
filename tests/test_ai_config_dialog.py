"""
测试AI配置对话框 - 验证保存按钮可见性
"""
import sys
from pathlib import Path
import tkinter as tk

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_dialog():
    """测试对话框"""
    print("🧪 测试AI配置对话框")
    print("=" * 50)
    print("检查项目:")
    print("  ✅ 窗口大小是否足够（500x550）")
    print("  ✅ 是否可以看到绿色的'💾 保存配置'按钮")
    print("  ✅ 是否可以看到灰色的'❌ 取消'按钮")
    print("  ✅ 底部是否有提示文字")
    print()
    
    try:
        from api_key_config import APIKeyConfigDialog
        
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        
        print("🚀 正在打开AI配置对话框...")
        print("💡 请检查对话框底部是否有绿色的保存按钮")
        
        dialog = APIKeyConfigDialog()
        result = dialog.show()
        
        if result:
            print("✅ 配置已保存!")
            print(f"  模型: {result['model_type']}")
            print(f"  API Key: {'已设置' if result.get('api_key') else '未设置'}")
        else:
            print("ℹ️ 用户取消了配置")
        
        root.destroy()
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("🔧 AI配置对话框修复验证")
    print("=" * 60)
    print("修复内容:")
    print("  🔧 窗口高度从400增加到550")
    print("  🔧 允许窗口大小调整")
    print("  🔧 减少组件间距")
    print("  🔧 确保按钮区域可见")
    print()
    
    test_dialog()
    
    print("\n" + "=" * 60)
    print("🎯 如果仍然看不到保存按钮，请:")
    print("  1. 尝试拖拽窗口边缘调整大小")
    print("  2. 向下滚动查看底部内容")
    print("  3. 检查屏幕分辨率设置")

if __name__ == "__main__":
    main()
