"""
测试GUI启动
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_gui_startup():
    """测试GUI启动"""
    print("🧪 测试GUI启动...")
    
    try:
        # 导入GUI模块
        from automotive_analysis_gui import AutomotiveAnalysisGUI
        import tkinter as tk
        
        print("✅ 模块导入成功")
        
        # 创建根窗口
        root = tk.Tk()
        print("✅ Tkinter根窗口创建成功")
        
        # 创建GUI应用
        app = AutomotiveAnalysisGUI(root)
        print("✅ GUI应用创建成功")
        
        # 检查AI配置状态
        if hasattr(app, 'ai_config'):
            if app.ai_config:
                print(f"✅ AI配置已加载: {app.ai_config.get('model_type', 'unknown')}")
            else:
                print("ℹ️ 未找到AI配置，将使用基础分析模式")
        else:
            print("❌ AI配置属性缺失")
        
        # 不启动主循环，直接销毁窗口
        root.destroy()
        print("✅ GUI测试完成")
        
        return True
        
    except Exception as e:
        print(f"❌ GUI启动测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 GUI启动测试")
    print("=" * 40)
    
    success = test_gui_startup()
    
    print("\n" + "=" * 40)
    if success:
        print("🎉 GUI启动测试成功！")
        print("\n💡 现在可以安全地运行:")
        print("  python 启动界面.py")
    else:
        print("❌ GUI启动测试失败")

if __name__ == "__main__":
    main()
