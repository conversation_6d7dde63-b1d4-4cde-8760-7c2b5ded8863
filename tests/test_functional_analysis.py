#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试深度功能逻辑分析的示例脚本
"""

from ai_requirement_reviewer import AIRequirementReviewer
import tempfile
import os
from docx import Document

def create_test_document_with_logic_issues():
    """创建包含逻辑问题的测试文档"""
    doc = Document()
    
    # 添加标题
    doc.add_heading('用户登录系统需求规范', 0)
    
    # 添加版本信息
    doc.add_paragraph('版本: V1.0')
    doc.add_paragraph('日期: 2025-07-31')
    
    # 添加功能需求 - 包含逻辑问题
    doc.add_heading('1. 功能需求', level=1)
    
    doc.add_heading('1.1 用户登录功能', level=2)
    doc.add_paragraph("""
    REQ-001: 用户登录验证
    描述：当用户输入用户名和密码时，系统需要验证用户身份。
    如果用户名存在且密码正确，则允许用户登录。
    用户连续输入错误密码3次后，账户将被锁定。
    系统需要记录所有登录尝试。
    """)
    
    doc.add_heading('1.2 数据处理功能', level=2)
    doc.add_paragraph("""
    REQ-002: 用户数据更新
    描述：用户可以更新个人信息，包括姓名、邮箱、电话等。
    系统需要同时更新用户表和用户配置表。
    更新操作需要在多个数据库节点间同步。
    系统支持多用户同时更新不同字段。
    """)
    
    doc.add_heading('1.3 会话管理功能', level=2)
    doc.add_paragraph("""
    REQ-003: 会话超时处理
    描述：用户会话在30分钟无操作后自动过期。
    系统需要清理过期会话数据。
    用户可以手动延长会话时间。
    系统需要处理并发会话清理操作。
    """)
    
    doc.add_heading('1.4 文件上传功能', level=2)
    doc.add_paragraph("""
    REQ-004: 文件上传处理
    描述：用户可以上传文件，文件大小限制为10MB。
    系统需要检查文件类型和内容。
    上传过程中如果网络中断，需要支持断点续传。
    系统需要处理大量并发上传请求。
    """)
    
    # 保存到临时文件
    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.docx')
    doc.save(temp_file.name)
    return temp_file.name

def test_functional_logic_analysis():
    """测试深度功能逻辑分析"""
    print("🔍 开始测试深度功能逻辑分析...")
    
    # 创建测试文档
    test_file = create_test_document_with_logic_issues()
    print(f"📄 创建测试文档: {test_file}")
    
    try:
        # 初始化评审器
        reviewer = AIRequirementReviewer()
        print("✅ 评审器初始化完成")
        
        # 执行评审
        print("🔍 开始深度功能逻辑分析...")
        result = reviewer.review_document(test_file)
        
        print(f"\n📊 分析结果:")
        print(f"总体评分: {result['overall_score']:.1f}")
        print(f"发现问题数: {result['total_issues']}")

        # 按类别统计问题
        category_counts = {}
        for issue in result['issues']:
            category = issue['category']
            category_counts[category] = category_counts.get(category, 0) + 1

        print(f"\n📋 问题分类统计:")
        for category, count in category_counts.items():
            print(f"  {category}: {count}个问题")

        # 显示关键的功能逻辑问题
        print(f"\n🚨 发现的关键功能逻辑问题:")
        logic_issues = [issue for issue in result['issues']
                       if any(keyword in issue['title'].lower()
                             for keyword in ['逻辑', '一致性', '并发', '边界', '事务'])]

        for i, issue in enumerate(logic_issues[:10], 1):  # 显示前10个
            print(f"\n{i}. {issue['title']}")
            print(f"   严重程度: {issue['severity']}")
            print(f"   描述: {issue['description']}")
            print(f"   建议: {issue['suggestion']}")

        # 显示AI分析结果
        if 'ai_analysis' in result and result['ai_analysis']:
            ai_analysis = result['ai_analysis']
            print(f"\n🤖 AI深度分析结果:")
            print(f"   置信度: {ai_analysis.get('confidence_score', 0):.2f}")
            print(f"   发现的测试风险: {len(ai_analysis.get('test_risks', []))}")
            print(f"   测试建议数: {len(ai_analysis.get('test_case_suggestions', []))}")

        print(f"\n📄 详细报告已生成: {result['report_path']}")

        # 验证是否发现了预期的逻辑问题
        expected_issues = [
            "条件分支",  # 登录验证缺少else分支
            "事务",      # 数据更新缺少事务处理
            "并发",      # 并发访问问题
            "边界"       # 文件大小边界处理
        ]

        found_issues = []
        for expected in expected_issues:
            for issue in result['issues']:
                if expected in issue['description'] or expected in issue['title']:
                    found_issues.append(expected)
                    break
        
        print(f"\n✅ 验证结果:")
        print(f"   预期发现的逻辑问题类型: {len(expected_issues)}")
        print(f"   实际发现的逻辑问题类型: {len(found_issues)}")
        print(f"   发现的问题类型: {found_issues}")
        
        if len(found_issues) >= len(expected_issues) * 0.5:  # 至少发现50%的预期问题
            print("🎉 深度功能逻辑分析测试通过！")
            return True
        else:
            print("⚠️  深度功能逻辑分析可能需要进一步优化")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    finally:
        # 清理临时文件
        if os.path.exists(test_file):
            os.unlink(test_file)
            print(f"🗑️  清理临时文件: {test_file}")

if __name__ == "__main__":
    success = test_functional_logic_analysis()
    exit(0 if success else 1)
