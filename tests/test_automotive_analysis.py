"""
测试汽车电子系统专项分析功能
"""
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from analysis_engine import AnalysisEngine
from document_parser import DocumentParser
from automotive_analyzer import AutomotiveAnalyzer
from report_generator import ReportGenerator

def test_automotive_analysis():
    """测试汽车电子系统分析功能"""
    print("🚗 开始测试汽车电子系统专项分析功能...")
    
    # 创建测试用的需求文档内容
    test_document_content = """
    前智能交互显示系统功能逻辑说明
    
    1. 闭锁功能
    当用户执行闭锁操作时，系统播放闭锁灯语动画。播放结束后，系统状态需要明确定义。
    
    2. 迎宾功能
    迎宾功能的触发条件与车辆档位相关。当迎宾功能结束后，如果收到交互灯语和指示灯语的请求，
    系统需要立即响应。
    
    3. 充电显示功能
    充电显示功能显示当前电量百分比。当充电到100%时，系统行为需要明确定义。
    电量显示的颗粒度以10%为单位，但95%电量的显示方式需要特别说明。
    
    4. 交互灯语和指示灯语
    交互灯语和指示灯语可能同时触发，两者的优先级关系需要明确定义。
    充电指示和临停灯语也存在优先级问题。
    
    5. 信号定义
    系统使用EL_WelcomeLightSt信号，但该信号在功能逻辑中的作用未明确描述。
    临停指示功能对应的指示开关信号名称需要明确。
    
    6. 通讯处理
    当单边屏幕出现通讯丢失时，系统的处理逻辑需要明确。
    ISD矩阵灯LED序号的排列规则需要提供。
    
    7. 故障处理
    故障的触发时间和恢复时间需要明确定义。
    当EL_FBLightMode变为OFF时，系统停止执行迎宾灯语，但灯的状态需要明确。
    
    8. 自定义灯语
    自定义灯语是否支持特殊字符和汉字，最大显示长度是否为16个字符。
    
    9. 诊断命令
    功能和诊断命令之间的逻辑关系，是否相互打断需要明确。
    """
    
    # 使用现有的Word文档进行测试
    test_file_path = "SYS-050-AHT RISD系统需求规范-V0.4-20250725.docx"
    if not os.path.exists(test_file_path):
        test_file_path = "SYS-050-T9M-SIDP系统需求规范-V1.3-20250716.docx"
    
    try:
        # 测试文档解析
        print("📄 解析测试文档...")
        parser = DocumentParser()
        parsed_data = parser.parse_document(test_file_path)
        print(f"✅ 文档解析完成，发现 {len(parsed_data.get('requirements', []))} 个需求")
        
        # 测试汽车电子系统专项分析
        print("🔍 执行汽车电子系统专项分析...")
        automotive_analyzer = AutomotiveAnalyzer()
        automotive_result = automotive_analyzer.analyze_automotive_requirements(parsed_data)
        
        print(f"📊 汽车电子系统专项评分: {automotive_result.automotive_score:.1f}")
        print(f"🔄 状态管理问题: {len(automotive_result.state_issues)} 个")
        print(f"⚡ 优先级冲突问题: {len(automotive_result.priority_issues)} 个")
        print(f"📡 信号接口问题: {len(automotive_result.signal_issues)} 个")
        print(f"⏱️ 时序逻辑问题: {len(automotive_result.timing_issues)} 个")
        print(f"⚠️ 边界条件问题: {len(automotive_result.boundary_issues)} 个")
        
        # 显示发现的具体问题
        all_automotive_issues = (automotive_result.state_issues + 
                               automotive_result.priority_issues + 
                               automotive_result.signal_issues + 
                               automotive_result.timing_issues + 
                               automotive_result.boundary_issues)
        
        if all_automotive_issues:
            print("\n🚨 发现的汽车电子系统问题:")
            for i, issue in enumerate(all_automotive_issues, 1):
                print(f"{i}. [{issue.severity.value.upper()}] {issue.title}")
                print(f"   描述: {issue.description}")
                print(f"   建议: {issue.suggestion}")
                print()
        
        # 测试完整分析引擎
        print("🔧 执行完整分析...")
        engine = AnalysisEngine()
        analysis_result = engine.analyze_document(test_file_path)
        
        print(f"📈 总体评分: {analysis_result.overall_score:.1f}")
        print(f"📋 发现问题总数: {len(analysis_result.issues)}")
        
        # 统计各类别问题数量
        category_counts = {}
        for issue in analysis_result.issues:
            category = issue.category.value
            category_counts[category] = category_counts.get(category, 0) + 1
        
        print("\n📊 问题分类统计:")
        for category, count in category_counts.items():
            print(f"  {category}: {count} 个")
        
        # 生成报告
        print("\n📝 生成分析报告...")
        report_generator = ReportGenerator()
        
        # 创建模拟的文档元数据
        from document_parser import DocumentMetadata
        metadata = DocumentMetadata(
            title="前智能交互显示系统功能逻辑说明",
            version="测试版本",
            author="测试作者",
            total_words=len(test_document_content),
            total_requirements=len(parsed_data.get('requirements', []))
        )
        
        report_path = report_generator.generate_report(analysis_result, metadata, "html")
        print(f"✅ 报告已生成: {report_path}")
        
        # 验证汽车电子系统专项评分是否包含在结果中
        if 'automotive_specific' in analysis_result.category_scores:
            print(f"🎯 汽车电子系统专项评分已集成: {analysis_result.category_scores['automotive_specific']:.1f}")
        else:
            print("⚠️ 汽车电子系统专项评分未正确集成")
        
        print("\n🎉 汽车电子系统专项分析测试完成!")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # 不需要清理，使用的是现有文档
        pass

def test_specific_automotive_issues():
    """测试特定的汽车电子系统问题检测"""
    print("\n🔍 测试特定汽车电子系统问题检测...")
    
    test_cases = [
        {
            "name": "闭锁功能状态未明确",
            "content": "闭锁功能播放结束后，系统进入某种状态。",
            "expected_issues": ["AUTO_STATE_001"]
        },
        {
            "name": "充电100%持续显示未明确", 
            "content": "充电显示功能显示电量到100%。",
            "expected_issues": ["AUTO_STATE_002"]
        },
        {
            "name": "交互灯语和指示灯语优先级未明确",
            "content": "系统支持交互灯语和指示灯语功能。",
            "expected_issues": ["AUTO_PRIORITY_001"]
        },
        {
            "name": "EL_WelcomeLightSt信号作用未明确",
            "content": "系统使用EL_WelcomeLightSt信号进行通讯。",
            "expected_issues": ["AUTO_SIGNAL_001"]
        }
    ]
    
    automotive_analyzer = AutomotiveAnalyzer()
    
    for test_case in test_cases:
        print(f"\n测试用例: {test_case['name']}")
        
        # 模拟解析数据
        parsed_data = {
            'raw_text': test_case['content'],
            'requirements': []
        }
        
        result = automotive_analyzer.analyze_automotive_requirements(parsed_data)
        all_issues = (result.state_issues + result.priority_issues + 
                     result.signal_issues + result.timing_issues + result.boundary_issues)
        
        found_issue_ids = [issue.id for issue in all_issues]
        
        for expected_id in test_case['expected_issues']:
            if expected_id in found_issue_ids:
                print(f"  ✅ 成功检测到问题: {expected_id}")
            else:
                print(f"  ❌ 未检测到预期问题: {expected_id}")
        
        if found_issue_ids:
            print(f"  📋 实际检测到的问题: {found_issue_ids}")
        else:
            print(f"  📋 未检测到任何问题")

if __name__ == "__main__":
    print("🚀 开始汽车电子系统专项分析功能测试")
    print("=" * 60)
    
    # 执行主要测试
    success = test_automotive_analysis()
    
    # 执行特定问题检测测试
    test_specific_automotive_issues()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ 所有测试完成!")
    else:
        print("❌ 测试过程中出现错误!")
