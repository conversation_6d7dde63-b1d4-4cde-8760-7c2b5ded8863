"""
AI分析功能演示
"""
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from analysis_engine import AnalysisEngine
from api_key_config import get_saved_config
from report_generator import ReportGenerator
from document_parser import DocumentParser, DocumentMetadata

def demonstrate_ai_vs_basic_analysis():
    """演示AI分析 vs 基础分析的对比"""
    print("🤖 AI分析 vs 基础分析对比演示")
    print("=" * 80)
    
    # 选择文档
    available_docs = [
        "SYS-050-AHT RISD系统需求规范-V0.4-20250725.docx",
        "SYS-050-T9M-SIDP系统需求规范-V1.3-20250716.docx"
    ]
    
    selected_doc = None
    for doc in available_docs:
        if os.path.exists(doc):
            selected_doc = doc
            break
    
    if not selected_doc:
        print("❌ 未找到测试文档")
        return
    
    print(f"📄 分析文档: {selected_doc}")
    print()
    
    # 检查AI配置
    ai_config = get_saved_config()
    
    if ai_config:
        print("🤖 AI配置已找到，将进行对比分析")
        print(f"  模型类型: {ai_config['model_type']}")
        print()
        
        # AI分析
        print("🔍 执行AI深度分析...")
        try:
            ai_engine = AnalysisEngine(
                ai_api_key=ai_config['api_key'], 
                ai_model_type=ai_config['model_type']
            )
            ai_result = ai_engine.analyze_document(selected_doc)
            print("✅ AI分析完成")
            
            # 基础分析
            print("🔍 执行基础分析...")
            basic_engine = AnalysisEngine()  # 不传入AI配置
            basic_result = basic_engine.analyze_document(selected_doc)
            print("✅ 基础分析完成")
            print()
            
            # 对比结果
            print("📊 分析结果对比:")
            print("=" * 60)
            print(f"{'指标':<20} {'AI分析':<15} {'基础分析':<15} {'差异'}")
            print("-" * 60)
            
            ai_issues = len(ai_result.issues)
            basic_issues = len(basic_result.issues)
            print(f"{'发现问题总数':<20} {ai_issues:<15} {basic_issues:<15} {ai_issues - basic_issues:+d}")
            
            ai_reviews = len(ai_result.review_questions) if ai_result.review_questions else 0
            basic_reviews = len(basic_result.review_questions) if basic_result.review_questions else 0
            print(f"{'评审问题数':<20} {ai_reviews:<15} {basic_reviews:<15} {ai_reviews - basic_reviews:+d}")
            
            ai_risks = len(ai_result.ai_insights.get('test_risks', []))
            basic_risks = len(basic_result.ai_insights.get('test_risks', []))
            print(f"{'AI洞察数':<20} {ai_risks:<15} {basic_risks:<15} {ai_risks - basic_risks:+d}")
            
            print(f"{'总体评分':<20} {ai_result.overall_score:<15.1f} {basic_result.overall_score:<15.1f} {ai_result.overall_score - basic_result.overall_score:+.1f}")
            print()
            
            # 显示AI独有的洞察
            if ai_result.ai_insights.get('test_risks'):
                print("🤖 AI独有的深度洞察:")
                print("-" * 40)
                for i, risk in enumerate(ai_result.ai_insights['test_risks'][:5], 1):
                    print(f"{i}. {risk}")
                print()
            
            # 生成对比报告
            print("📝 生成对比报告...")
            report_generator = ReportGenerator()
            
            parser = DocumentParser()
            parsed_data = parser.parse_document(selected_doc)
            metadata = parsed_data.get('metadata')
            
            if not metadata:
                metadata = DocumentMetadata(
                    title=f"AI分析对比 - {os.path.basename(selected_doc)}",
                    version="对比版本",
                    author="AI分析师",
                    total_words=0,
                    total_requirements=len(parsed_data.get('requirements', []))
                )
            
            ai_report_path = report_generator.generate_report(ai_result, metadata, "html")
            print(f"✅ AI分析报告: {ai_report_path}")
            
            basic_report_path = report_generator.generate_report(basic_result, metadata, "html")
            print(f"✅ 基础分析报告: {basic_report_path}")
            
        except Exception as e:
            print(f"❌ AI分析失败: {e}")
            print("将只进行基础分析...")
            
            basic_engine = AnalysisEngine()
            basic_result = basic_engine.analyze_document(selected_doc)
            
            print("📊 基础分析结果:")
            print(f"  • 发现问题: {len(basic_result.issues)} 个")
            print(f"  • 评审问题: {len(basic_result.review_questions) if basic_result.review_questions else 0} 个")
            print(f"  • 总体评分: {basic_result.overall_score:.1f}")
    
    else:
        print("⚠️ 未找到AI配置，只进行基础分析")
        print("💡 运行 'python test_ai_config.py' 来配置AI模型")
        print()
        
        # 只进行基础分析
        print("🔍 执行基础分析...")
        basic_engine = AnalysisEngine()
        basic_result = basic_engine.analyze_document(selected_doc)
        print("✅ 基础分析完成")
        
        print("📊 基础分析结果:")
        print(f"  • 发现问题: {len(basic_result.issues)} 个")
        print(f"  • 评审问题: {len(basic_result.review_questions) if basic_result.review_questions else 0} 个")
        print(f"  • 总体评分: {basic_result.overall_score:.1f}")

def show_ai_features():
    """展示AI分析功能特色"""
    print("\n🌟 AI分析功能特色")
    print("=" * 80)
    
    features = [
        ("🧠 深度理解", "AI能够理解复杂的语义和上下文关系"),
        ("🔍 智能发现", "发现预设规则无法覆盖的隐藏问题"),
        ("💭 专家视角", "以20年经验需求分析师的视角提出疑问"),
        ("🎯 个性化分析", "针对具体文档内容的定制化深度分析"),
        ("📋 结构化输出", "生成结构化的评审问题和建议"),
        ("🚨 风险识别", "识别可能导致系统缺陷的深层风险"),
        ("💡 创新洞察", "提供人工规则难以发现的创新观点"),
        ("🔄 持续学习", "基于最新的AI模型能力持续改进")
    ]
    
    for title, desc in features:
        print(f"{title}: {desc}")
    
    print("\n🆚 AI分析 vs 基础分析对比:")
    print("-" * 60)
    
    comparisons = [
        ("分析深度", "深层语义理解", "规则模式匹配"),
        ("问题发现", "智能识别隐藏问题", "预设规则检查"),
        ("个性化", "针对文档定制分析", "通用规则应用"),
        ("创新性", "提供新颖观点", "基于既定模式"),
        ("适应性", "适应不同领域", "固定规则集"),
        ("分析速度", "10-30秒", "1-3秒"),
        ("网络依赖", "需要网络连接", "完全离线"),
        ("成本", "需要API费用", "完全免费")
    ]
    
    print(f"{'维度':<12} {'AI分析':<20} {'基础分析'}")
    print("-" * 60)
    for dimension, ai_feature, basic_feature in comparisons:
        print(f"{dimension:<12} {ai_feature:<20} {basic_feature}")

def show_supported_models():
    """显示支持的AI模型"""
    print("\n🤖 支持的AI模型")
    print("=" * 80)
    
    models = [
        {
            "name": "Google Gemini 2.0 Flash",
            "provider": "Google",
            "features": ["快速响应", "多模态理解", "长文本处理", "中文优化"],
            "cost": "相对较低",
            "recommended": True
        },
        {
            "name": "OpenAI GPT-3.5-turbo",
            "provider": "OpenAI",
            "features": ["成熟稳定", "广泛应用", "高质量输出", "API丰富"],
            "cost": "中等",
            "recommended": False
        }
    ]
    
    for model in models:
        status = "🌟 推荐" if model["recommended"] else "✅ 支持"
        print(f"{status} {model['name']}")
        print(f"  提供商: {model['provider']}")
        print(f"  特色: {', '.join(model['features'])}")
        print(f"  成本: {model['cost']}")
        print()

def main():
    """主函数"""
    # 显示AI功能特色
    show_ai_features()
    
    # 显示支持的模型
    show_supported_models()
    
    # 演示分析对比
    demonstrate_ai_vs_basic_analysis()
    
    print("\n🎉 AI分析功能演示完成!")
    print("=" * 80)
    
    print("\n💡 下一步建议:")
    suggestions = [
        "🔧 运行 'python test_ai_config.py' 配置AI模型",
        "🚀 运行 'python 启动界面.py' 启动图形界面",
        "📊 在GUI中体验AI分析的强大功能",
        "📝 查看生成的HTML报告对比AI和基础分析结果",
        "🎯 将AI发现的问题用于实际的需求评审会议"
    ]
    
    for suggestion in suggestions:
        print(f"  {suggestion}")

if __name__ == "__main__":
    main()
