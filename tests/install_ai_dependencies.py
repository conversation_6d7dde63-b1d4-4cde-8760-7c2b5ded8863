"""
安装AI依赖库
"""
import subprocess
import sys
import os

def install_package(package):
    """安装Python包"""
    try:
        print(f"正在安装 {package}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ {package} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {package} 安装失败: {e}")
        return False

def main():
    """主函数"""
    print("🤖 安装AI分析依赖库")
    print("=" * 50)
    
    # 需要安装的包
    packages = [
        "google-generativeai",  # Google Gemini
        "openai",              # OpenAI GPT
    ]
    
    success_count = 0
    
    for package in packages:
        if install_package(package):
            success_count += 1
        print()
    
    print("=" * 50)
    print(f"安装完成: {success_count}/{len(packages)} 个包安装成功")
    
    if success_count == len(packages):
        print("✅ 所有依赖安装成功！现在可以配置AI模型了。")
    else:
        print("⚠️ 部分依赖安装失败，可能影响AI功能。")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
