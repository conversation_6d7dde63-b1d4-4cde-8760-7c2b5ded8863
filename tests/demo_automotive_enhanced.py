"""
汽车电子系统需求分析增强功能演示
根据需求疑问汇总重新调整的项目代码演示
"""
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from analysis_engine import AnalysisEngine
from automotive_analyzer import AutomotiveAnalyzer
from report_generator import ReportGenerator
from document_parser import DocumentParser, DocumentMetadata

def demonstrate_enhanced_analysis():
    """演示增强的汽车电子系统分析功能"""
    print("🚗 汽车电子系统需求分析增强功能演示")
    print("=" * 80)
    print("基于需求疑问汇总，重新调整项目代码以支持细粒度问题分析")
    print()
    
    # 显示增强的分析类别
    print("📊 新增的分析类别:")
    enhanced_categories = [
        ("状态管理", "分析功能结束后状态定义、状态持续性等问题"),
        ("信号接口规范", "检查信号定义完整性、命名一致性、通讯故障处理"),
        ("边界条件", "分析数值边界、字符限制、档位关联等边界问题"),
        ("功能冲突处理", "检查功能优先级冲突、并发处理、诊断命令交互"),
        ("时序逻辑", "分析功能执行时序、故障时间定义、响应时延"),
        ("错误处理", "检查异常场景处理、错误恢复、安全状态保障"),
        ("汽车电子系统专项", "针对汽车电子系统特有问题的综合分析")
    ]
    
    for i, (category, description) in enumerate(enhanced_categories, 1):
        print(f"{i}. {category}: {description}")
    print()
    
    # 显示能够检测的具体问题
    print("🔍 能够检测的具体问题类型:")
    specific_issues = [
        "闭锁功能播放结束后是否为常亮状态",
        "迎宾功能触发条件与档位的关系",
        "迎宾功能结束后是否立刻响应交互灯语和指示灯语请求",
        "交互灯语和指示灯语的优先级关系",
        "充电到100%时是否需要持续显示",
        "EL_FBLightMode变为OFF时的处理逻辑",
        "故障的触发和恢复时间定义",
        "自定义灯语是否支持特殊字符、汉字及最大长度",
        "功能和诊断命令之间的逻辑关系",
        "ISD矩阵灯LED序号排列规则",
        "单边屏幕通讯丢失时的处理逻辑",
        "电量显示颗粒度和95%电量的显示方式",
        "临停指示对应的指示开关信号名称",
        "充电指示和临停灯语的优先级关系",
        "EL_WelcomeLightSt信号在功能逻辑中的作用"
    ]
    
    for i, issue in enumerate(specific_issues, 1):
        print(f"{i:2d}. {issue}")
    print()
    
    # 选择文档进行分析
    available_docs = [
        "SYS-050-AHT RISD系统需求规范-V0.4-20250725.docx",
        "SYS-050-T9M-SIDP系统需求规范-V1.3-20250716.docx"
    ]
    
    selected_doc = None
    for doc in available_docs:
        if os.path.exists(doc):
            selected_doc = doc
            break
    
    if not selected_doc:
        print("❌ 未找到可用的需求文档")
        return
    
    print(f"📄 分析文档: {selected_doc}")
    print()
    
    # 执行增强分析
    print("🔧 执行增强的需求分析...")
    engine = AnalysisEngine()
    analysis_result = engine.analyze_document(selected_doc)
    
    # 显示分析结果
    print(f"📈 总体评分: {analysis_result.overall_score:.1f}/100")
    print(f"📋 发现问题总数: {len(analysis_result.issues)}")
    print()
    
    # 显示各类别评分
    print("📊 各类别评分:")
    category_names = {
        'completeness': '完整性',
        'clarity': '清晰度', 
        'consistency': '一致性',
        'feasibility': '可行性',
        'testability': '可测试性',
        'maintainability': '可维护性',
        'test_design': '测试设计导向',
        'trigger_conditions': '触发条件',
        'exit_conditions': '退出条件',
        'priority_clarity': '优先级明确性',
        'state_management': '状态管理',
        'signal_interface': '信号接口规范',
        'boundary_conditions': '边界条件',
        'function_conflicts': '功能冲突处理',
        'timing_logic': '时序逻辑',
        'error_handling': '错误处理',
        'automotive_specific': '汽车电子系统专项'
    }
    
    for category, score in analysis_result.category_scores.items():
        name = category_names.get(category, category)
        grade = "优秀" if score >= 90 else "良好" if score >= 80 else "中等" if score >= 70 else "及格" if score >= 60 else "不及格"
        print(f"  {name}: {score:.1f} ({grade})")
    print()
    
    # 统计各类别问题
    category_counts = {}
    automotive_issues = []
    
    for issue in analysis_result.issues:
        category = issue.category.value
        category_counts[category] = category_counts.get(category, 0) + 1
        
        # 收集汽车电子系统特有问题
        if category in ['state_management', 'signal_interface', 'boundary_conditions', 
                       'function_conflicts', 'timing_logic', 'error_handling']:
            automotive_issues.append(issue)
    
    print("📊 问题分类统计:")
    for category, count in sorted(category_counts.items()):
        name = category_names.get(category, category)
        print(f"  {name}: {count} 个")
    print()
    
    # 显示汽车电子系统特有问题
    if automotive_issues:
        print("🚨 发现的汽车电子系统特有问题:")
        for i, issue in enumerate(automotive_issues, 1):
            severity_emoji = {
                'critical': '🔴',
                'high': '🟠', 
                'medium': '🟡',
                'low': '🟢',
                'info': '🔵'
            }
            emoji = severity_emoji.get(issue.severity.value, '⚪')
            print(f"{i:2d}. {emoji} [{issue.severity.value.upper()}] {issue.title}")
            print(f"     描述: {issue.description}")
            print(f"     建议: {issue.suggestion}")
            print()
    else:
        print("✅ 未发现汽车电子系统特有问题")
        print()
    
    # 生成报告
    print("📝 生成增强分析报告...")
    report_generator = ReportGenerator()
    
    # 解析文档元数据
    parser = DocumentParser()
    parsed_data = parser.parse_document(selected_doc)
    metadata = parsed_data.get('metadata')
    
    if not metadata:
        metadata = DocumentMetadata(
            title=os.path.basename(selected_doc),
            version="未知版本",
            author="未知作者",
            total_words=0,
            total_requirements=len(parsed_data.get('requirements', []))
        )
    
    report_path = report_generator.generate_report(analysis_result, metadata, "html")
    print(f"✅ 增强分析报告已生成: {report_path}")
    print()
    
    # 显示增强功能总结
    print("🎯 增强功能总结:")
    enhancements = [
        "✅ 新增6个汽车电子系统专项分析类别",
        "✅ 新增30+个专项分析规则",
        "✅ 增强AI分析器以理解汽车电子系统特定逻辑",
        "✅ 新增汽车电子系统专项分析器",
        "✅ 增强报告生成器以包含汽车电子系统分析结果",
        "✅ 新增汽车电子系统测试建议生成",
        "✅ 支持细粒度问题检测和分析"
    ]
    
    for enhancement in enhancements:
        print(f"  {enhancement}")
    print()
    
    print("🎉 汽车电子系统需求分析增强功能演示完成!")
    print("=" * 80)

def show_problem_mapping():
    """显示需求疑问到检测规则的映射"""
    print("\n📋 需求疑问到检测规则映射:")
    print("=" * 80)
    
    problem_mappings = [
        {
            "问题": "闭锁功能，播放结束后是常亮状态吗？",
            "检测规则": "SM_001 - 状态转换逻辑完整性",
            "具体检测": "AUTO_STATE_001 - 闭锁功能结束后状态未明确"
        },
        {
            "问题": "迎宾功能，触发条件与档位有关系吗",
            "检测规则": "BC_003 - 档位关联逻辑",
            "具体检测": "AUTO_BOUNDARY_003 - 迎宾功能与档位关系未明确"
        },
        {
            "问题": "迎宾功能结束后是否立刻响应交互灯语和指示灯语的请求？",
            "检测规则": "SM_004 - 状态变更响应逻辑",
            "具体检测": "AUTO_STATE_003 - 迎宾功能结束后响应机制未明确"
        },
        {
            "问题": "交互灯语和指示灯语的优先级？",
            "检测规则": "FC_001 - 功能优先级冲突分析",
            "具体检测": "AUTO_PRIORITY_001 - 交互灯语和指示灯语优先级未明确"
        },
        {
            "问题": "充电显示，充电到100%时，是否需要持续显示",
            "检测规则": "SM_002 - 状态持续性规范",
            "具体检测": "AUTO_STATE_002 - 充电100%时持续显示规则未明确"
        },
        {
            "问题": "EL_WelcomeLightSt信号在功能逻辑中的作用",
            "检测规则": "SI_001 - 信号定义完整性",
            "具体检测": "AUTO_SIGNAL_001 - EL_WelcomeLightSt信号作用未明确"
        },
        {
            "问题": "故障的触发和恢复时间？",
            "检测规则": "TL_002 - 故障时间定义",
            "具体检测": "AUTO_TIMING_001 - 故障触发和恢复时间未定义"
        },
        {
            "问题": "电量显示的颗粒度以10%为单位的吗，95%应该如何显示",
            "检测规则": "BC_001 - 数值边界处理",
            "具体检测": "AUTO_BOUNDARY_001 - 电量显示颗粒度未明确"
        },
        {
            "问题": "自定义灯语是否支持特殊字符，汉字？最大显示长度也是16？",
            "检测规则": "BC_002 - 字符限制规范",
            "具体检测": "AUTO_BOUNDARY_002 - 自定义灯语字符支持未明确"
        },
        {
            "问题": "功能和诊断命令之间的逻辑，例如是否相互打断",
            "检测规则": "FC_004 - 诊断命令与功能交互",
            "具体检测": "AUTO_PRIORITY_002 - 诊断命令与功能交互不明确"
        }
    ]
    
    for i, mapping in enumerate(problem_mappings, 1):
        print(f"{i:2d}. 问题: {mapping['问题']}")
        print(f"    检测规则: {mapping['检测规则']}")
        print(f"    具体检测: {mapping['具体检测']}")
        print()

if __name__ == "__main__":
    # 执行演示
    demonstrate_enhanced_analysis()
    
    # 显示问题映射
    show_problem_mapping()
