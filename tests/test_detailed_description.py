#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试详细问题描述功能的示例脚本
"""

from ai_requirement_reviewer import AIRequirementReviewer
import tempfile
import os
from docx import Document

def create_complex_test_document():
    """创建包含复杂逻辑问题的测试文档"""
    doc = Document()
    
    # 添加标题
    doc.add_heading('电商订单处理系统需求规范', 0)
    
    # 添加版本信息
    doc.add_paragraph('版本: V2.0')
    doc.add_paragraph('日期: 2025-07-31')
    
    # 添加复杂的功能需求
    doc.add_heading('1. 订单处理功能', level=1)
    
    doc.add_heading('1.1 订单创建流程', level=2)
    doc.add_paragraph("""
    REQ-001: 订单创建与验证
    描述：用户提交订单时，系统需要验证商品库存、用户余额、优惠券有效性。
    如果库存充足且用户余额足够，则创建订单。
    系统需要同时更新库存表、用户余额表、订单表。
    如果任何一步失败，需要回滚所有操作。
    支持多用户同时下单同一商品。
    订单金额超过1000元需要风控审核。
    """)
    
    doc.add_heading('1.2 支付处理流程', level=2)
    doc.add_paragraph("""
    REQ-002: 支付流程处理
    描述：用户选择支付方式后，系统调用第三方支付接口。
    支付成功后更新订单状态为已支付。
    支付失败后订单状态保持为待支付。
    支付超时（30分钟）后自动取消订单。
    系统需要处理支付回调的幂等性问题。
    支付过程中如果网络异常，需要查询支付状态。
    """)
    
    doc.add_heading('1.3 库存管理功能', level=2)
    doc.add_paragraph("""
    REQ-003: 库存扣减与恢复
    描述：下单时预扣库存，支付成功后正式扣减。
    支付失败或订单取消时恢复库存。
    库存不足时拒绝下单。
    系统需要防止超卖问题。
    支持库存预警，低于阈值时通知采购。
    库存数据需要实时同步到多个仓库。
    """)
    
    doc.add_heading('1.4 优惠券系统', level=2)
    doc.add_paragraph("""
    REQ-004: 优惠券使用逻辑
    描述：用户可以使用优惠券抵扣订单金额。
    每张优惠券只能使用一次。
    优惠券有使用条件限制（最低消费金额、商品类别等）。
    过期优惠券不能使用。
    系统需要防止优惠券重复使用。
    优惠券使用后立即标记为已使用。
    """)
    
    doc.add_heading('1.5 订单状态管理', level=2)
    doc.add_paragraph("""
    REQ-005: 订单状态流转
    描述：订单状态包括：待支付、已支付、已发货、已收货、已完成、已取消。
    状态流转必须按照预定义的规则进行。
    某些状态不可逆转（如已发货不能回到待支付）。
    状态变更需要记录操作日志。
    异常情况下需要人工干预处理。
    """)
    
    # 保存到临时文件
    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.docx')
    doc.save(temp_file.name)
    return temp_file.name

def test_detailed_problem_description():
    """测试详细问题描述功能"""
    print("🔍 开始测试详细问题描述功能...")
    
    # 创建测试文档
    test_file = create_complex_test_document()
    print(f"📄 创建复杂测试文档: {test_file}")
    
    try:
        # 初始化评审器
        reviewer = AIRequirementReviewer()
        print("✅ 评审器初始化完成")
        
        # 执行评审
        print("🔍 开始详细分析...")
        result = reviewer.review_document(test_file)
        
        print(f"\n📊 分析结果:")
        print(f"总体评分: {result['overall_score']:.1f}")
        print(f"发现问题数: {result['total_issues']}")
        
        # 展示问题描述的详细程度
        print(f"\n📋 问题描述详细程度分析:")
        
        description_lengths = []
        for issue in result['issues']:
            desc_length = len(issue['description'])
            description_lengths.append(desc_length)
        
        if description_lengths:
            avg_length = sum(description_lengths) / len(description_lengths)
            max_length = max(description_lengths)
            min_length = min(description_lengths)
            
            print(f"   平均描述长度: {avg_length:.1f} 字符")
            print(f"   最长描述: {max_length} 字符")
            print(f"   最短描述: {min_length} 字符")
        
        # 展示几个典型的详细问题描述
        print(f"\n🔍 典型问题描述示例:")
        
        # 选择几个有代表性的问题
        sample_issues = []
        categories_shown = set()
        
        for issue in result['issues']:
            category = issue['category']
            if category not in categories_shown and len(sample_issues) < 5:
                sample_issues.append(issue)
                categories_shown.add(category)
        
        for i, issue in enumerate(sample_issues, 1):
            print(f"\n{i}. 【{issue['category']}】{issue['title']}")
            print(f"   问题描述: {issue['description']}")
            print(f"   严重程度: {issue['severity']}")
            print(f"   改进建议: {issue['suggestion']}")
            print(f"   描述长度: {len(issue['description'])} 字符")
        
        # 分析问题描述的质量
        detailed_issues = [issue for issue in result['issues'] 
                          if len(issue['description']) > 50]  # 认为超过50字符的描述比较详细
        
        print(f"\n📈 问题描述质量统计:")
        print(f"   总问题数: {len(result['issues'])}")
        print(f"   详细描述问题数: {len(detailed_issues)}")
        print(f"   详细描述比例: {len(detailed_issues)/len(result['issues'])*100:.1f}%")
        
        # 检查是否包含具体的技术细节
        technical_keywords = ['事务', '并发', '死锁', '一致性', '幂等', '回滚', '同步', '异步', '缓存', '队列',
                             '溢出', '空值', '边界', '竞争', '锁', '资源', '内存', '连接', '异常', '验证',
                             'CPU', '数据库', '网络', '性能', '安全', '漏洞', '风险', '检查', '处理']
        technical_issues = []

        for issue in result['issues']:
            for keyword in technical_keywords:
                if keyword in issue['description'] or keyword in issue['title']:
                    technical_issues.append(issue)
                    break
        
        print(f"   包含技术细节的问题数: {len(technical_issues)}")
        print(f"   技术细节覆盖率: {len(technical_issues)/len(result['issues'])*100:.1f}%")
        
        print(f"\n📄 详细报告已生成: {result['report_path']}")
        
        # 验证改进效果
        success_criteria = [
            len(detailed_issues) >= len(result['issues']) * 0.7,  # 70%的问题有详细描述
            len(technical_issues) >= 3,  # 至少3个问题包含技术细节
            avg_length >= 40  # 平均描述长度至少40字符
        ]
        
        passed_criteria = sum(success_criteria)
        print(f"\n✅ 改进效果验证:")
        print(f"   通过的质量标准: {passed_criteria}/3")
        
        if passed_criteria >= 2:
            print("🎉 问题描述详细程度测试通过！")
            return True
        else:
            print("⚠️  问题描述详细程度需要进一步改进")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # 清理临时文件
        if os.path.exists(test_file):
            os.unlink(test_file)
            print(f"🗑️  清理临时文件: {test_file}")

if __name__ == "__main__":
    success = test_detailed_problem_description()
    exit(0 if success else 1)
