#!/usr/bin/env python3
"""
测试UI状态改进功能
验证分析模式提示是否正常工作
"""

import tkinter as tk
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from automotive_analysis_gui import AutomotiveAnalysisGUI
from api_key_config import get_saved_config

def test_ui_status_display():
    """测试UI状态显示功能"""
    print("🧪 测试UI状态显示改进功能")
    print("="*50)
    
    # 创建主窗口
    root = tk.Tk()
    app = AutomotiveAnalysisGUI(root)
    
    print("✅ GUI界面已创建")
    
    # 测试不同的AI配置状态
    print("\n📊 测试不同AI配置状态:")
    
    # 1. 测试无AI配置状态
    print("1. 测试无AI配置状态...")
    app.ai_config = None
    app.update_ai_status()
    print(f"   状态标签: {app.ai_status_label.cget('text')}")
    print(f"   模式指示器: {app.mode_indicator.cget('text')}")
    
    # 2. 测试Gemini配置状态
    print("2. 测试Gemini配置状态...")
    app.ai_config = {
        'model_type': 'gemini',
        'api_key': 'test_gemini_key_1234567890'
    }
    app.update_ai_status()
    print(f"   状态标签: {app.ai_status_label.cget('text')}")
    print(f"   模式指示器: {app.mode_indicator.cget('text')}")
    
    # 3. 测试OpenAI配置状态
    print("3. 测试OpenAI配置状态...")
    app.ai_config = {
        'model_type': 'openai',
        'api_key': 'test_openai_key_1234567890'
    }
    app.update_ai_status()
    print(f"   状态标签: {app.ai_status_label.cget('text')}")
    print(f"   模式指示器: {app.mode_indicator.cget('text')}")
    
    # 4. 测试未知模型状态
    print("4. 测试未知模型状态...")
    app.ai_config = {
        'model_type': 'unknown',
        'api_key': 'test_unknown_key_1234567890'
    }
    app.update_ai_status()
    print(f"   状态标签: {app.ai_status_label.cget('text')}")
    print(f"   模式指示器: {app.mode_indicator.cget('text')}")
    
    print("\n🔍 测试AI可用性检查功能:")
    
    # 测试AI可用性检查
    app.ai_config = None
    app.analysis_engine = None
    availability = app._check_ai_availability()
    print(f"   无AI配置时的可用性: {availability}")
    
    print("\n📈 测试分析模式确定功能:")
    
    # 创建模拟分析结果
    class MockAnalysisResult:
        def __init__(self, confidence_score):
            self.ai_insights = {'confidence_score': confidence_score}
    
    # 测试不同置信度分数
    test_scores = [0.9, 0.7, 0.5]
    for score in test_scores:
        mock_result = MockAnalysisResult(score)
        mode = app._determine_actual_analysis_mode(mock_result)
        print(f"   置信度 {score}: {mode}")
    
    print("\n✅ UI状态显示测试完成")
    print("\n💡 使用说明:")
    print("   - 🚀 表示AI深度分析模式")
    print("   - 📝 表示基础分析模式")
    print("   - ⚠️ 表示备用分析方案")
    print("   - ❌表示分析失败")
    
    # 显示界面供手动测试
    print("\n🖥️ 启动GUI界面进行手动测试...")
    print("   请观察左侧状态面板中的分析模式指示器")
    print("   可以尝试配置AI或进行分析来观察状态变化")
    
    # 恢复原始配置
    original_config = get_saved_config()
    if original_config:
        app.ai_config = original_config
        app.update_ai_status()
    
    root.mainloop()

def test_analysis_mode_detection():
    """测试分析模式检测功能"""
    print("\n🔬 测试分析模式检测功能")
    print("="*50)
    
    # 创建临时GUI实例用于测试
    root = tk.Tk()
    root.withdraw()  # 隐藏窗口
    app = AutomotiveAnalysisGUI(root)
    
    # 测试不同的AI配置和分析结果组合
    test_cases = [
        {
            'name': 'AI深度分析',
            'ai_config': {'model_type': 'gemini', 'api_key': 'test_key'},
            'confidence_score': 0.9,
            'expected_mode': '🚀'
        },
        {
            'name': '备用分析方案',
            'ai_config': {'model_type': 'gemini', 'api_key': 'test_key'},
            'confidence_score': 0.7,
            'expected_mode': '📝 备用'
        },
        {
            'name': '基础分析模式',
            'ai_config': None,
            'confidence_score': 0.5,
            'expected_mode': '📝 基础'
        }
    ]
    
    for case in test_cases:
        print(f"\n测试案例: {case['name']}")
        app.ai_config = case['ai_config']
        
        # 创建模拟分析结果
        class MockResult:
            def __init__(self, confidence_score):
                self.ai_insights = {'confidence_score': confidence_score}
        
        mock_result = MockResult(case['confidence_score'])
        detected_mode = app._determine_actual_analysis_mode(mock_result)
        
        print(f"   AI配置: {case['ai_config']}")
        print(f"   置信度分数: {case['confidence_score']}")
        print(f"   检测到的模式: {detected_mode}")
        print(f"   期望模式包含: {case['expected_mode']}")
        print(f"   测试结果: {'✅ 通过' if case['expected_mode'] in detected_mode else '❌ 失败'}")
    
    root.destroy()
    print("\n✅ 分析模式检测测试完成")

if __name__ == "__main__":
    print("🚗 汽车电子系统需求分析平台 - UI状态改进测试")
    print("="*60)
    
    try:
        # 运行测试
        test_analysis_mode_detection()
        test_ui_status_display()
        
    except KeyboardInterrupt:
        print("\n\n⏹️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n🎯 测试完成！")
