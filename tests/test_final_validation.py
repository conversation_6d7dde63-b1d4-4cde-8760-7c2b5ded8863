#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
最终验证测试 - 验证详细问题描述功能的完整改进效果
"""

import tempfile
import os
from docx import Document
from ai_requirement_reviewer import AIRequirementReviewer

def create_comprehensive_test_document():
    """创建综合测试文档"""
    doc = Document()
    doc.add_heading('电商平台需求规格说明书', 0)
    doc.add_paragraph('版本: 1.0')
    doc.add_paragraph('日期: 2025-07-31')
    
    # 添加复杂的功能需求，包含各种深度分析场景
    doc.add_heading('1. 核心业务功能', level=1)
    
    doc.add_heading('1.1 订单处理系统', level=2)
    doc.add_paragraph("""
    REQ-001: 订单创建与库存管理
    描述：用户提交订单时，系统需要验证商品库存、用户余额、优惠券有效性。
    如果库存充足且用户余额足够，则创建订单，否则拒绝订单。
    系统需要同时更新库存表、用户余额表、订单表、日志表。
    支持多用户并发下单同一商品，需要处理库存竞争问题。
    订单金额范围为0.01-99999.99元，需要处理数值边界情况。
    当库存为空值或null时，系统需要特殊处理逻辑。
    在分布式环境下，多个节点需要保持数据一致性。
    """)
    
    doc.add_heading('1.2 支付处理流程', level=2)
    doc.add_paragraph("""
    REQ-002: 支付与事务管理
    描述：用户选择支付方式后，系统调用第三方支付接口进行支付。
    支付过程涉及多个锁操作：账户锁、订单锁、库存锁。
    支付成功后更新订单状态、扣减余额、记录交易日志。
    支付失败时需要回滚所有操作，恢复原始状态。
    支付超时（30分钟）后循环检查支付状态，直到确认结果。
    系统需要处理支付回调的幂等性和重复请求问题。
    """)
    
    doc.add_heading('1.3 用户权限管理', level=2)
    doc.add_paragraph("""
    REQ-003: 权限验证与安全控制
    描述：系统需要验证用户权限，根据用户角色分配不同功能。
    管理员可以查看所有订单，普通用户只能查看自己的订单。
    VIP用户享有优先购买权，普通用户需要排队等待。
    系统需要处理权限规则冲突的情况。
    用户输入的字符串长度限制为1-500字符。
    """)
    
    doc.add_heading('2. 技术需求', level=1)
    
    doc.add_heading('2.1 性能要求', level=2)
    doc.add_paragraph("""
    系统需要支持10000个并发用户同时访问。
    数据库连接池大小为100，需要合理管理连接资源。
    缓存系统需要处理内存不足的情况。
    """)
    
    doc.add_heading('2.2 数据处理', level=2)
    doc.add_paragraph("""
    系统需要处理大量数据的插入、修改、删除操作。
    数据同步需要在主从数据库之间保持一致性。
    文件上传大小限制为10MB，需要处理文件大小溢出。
    """)
    
    # 保存文档
    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.docx')
    doc.save(temp_file.name)
    return temp_file.name

def analyze_description_quality(issues):
    """分析问题描述质量"""
    if not issues:
        return {
            'avg_length': 0,
            'max_length': 0,
            'min_length': 0,
            'detailed_count': 0,
            'detailed_ratio': 0,
            'technical_count': 0,
            'technical_ratio': 0
        }
    
    lengths = [len(issue['description']) for issue in issues]
    avg_length = sum(lengths) / len(lengths)
    max_length = max(lengths)
    min_length = min(lengths)
    
    # 详细描述：长度超过50字符
    detailed_count = sum(1 for length in lengths if length > 50)
    detailed_ratio = detailed_count / len(issues) * 100
    
    # 技术细节：包含技术关键词
    technical_keywords = [
        '事务', '并发', '死锁', '一致性', '幂等', '回滚', '同步', '异步', '缓存', '队列',
        '溢出', '空值', '边界', '竞争', '锁', '资源', '内存', '连接', '异常', '验证',
        'CPU', '数据库', '网络', '性能', '安全', '漏洞', '风险', '检查', '处理',
        '算法', '协议', '接口', 'API', 'SQL', 'HTTP', 'JSON', 'XML'
    ]
    
    technical_count = 0
    for issue in issues:
        for keyword in technical_keywords:
            if keyword in issue['description'] or keyword in issue['title']:
                technical_count += 1
                break
    
    technical_ratio = technical_count / len(issues) * 100
    
    return {
        'avg_length': avg_length,
        'max_length': max_length,
        'min_length': min_length,
        'detailed_count': detailed_count,
        'detailed_ratio': detailed_ratio,
        'technical_count': technical_count,
        'technical_ratio': technical_ratio
    }

def main():
    print("🎯 开始最终验证测试...")
    
    # 创建测试文档
    test_file = create_comprehensive_test_document()
    print(f"📄 创建综合测试文档: {test_file}")
    
    try:
        # 初始化评审器
        reviewer = AIRequirementReviewer()
        print("✅ 评审器初始化完成")
        
        # 执行分析
        print("🔍 开始综合分析...")
        result = reviewer.review_document(test_file)
        
        print(f"\n📊 分析结果:")
        print(f"总体评分: {result['overall_score']}")
        print(f"发现问题数: {len(result['issues'])}")
        
        # 分析问题描述质量
        quality = analyze_description_quality(result['issues'])
        
        print(f"\n📋 问题描述质量分析:")
        print(f"   平均描述长度: {quality['avg_length']:.1f} 字符")
        print(f"   最长描述: {quality['max_length']} 字符")
        print(f"   最短描述: {quality['min_length']} 字符")
        print(f"   详细描述问题数: {quality['detailed_count']}")
        print(f"   详细描述比例: {quality['detailed_ratio']:.1f}%")
        print(f"   包含技术细节的问题数: {quality['technical_count']}")
        print(f"   技术细节覆盖率: {quality['technical_ratio']:.1f}%")
        
        # 显示最详细的问题描述示例
        print(f"\n🔍 最详细的问题描述示例:")
        sorted_issues = sorted(result['issues'], key=lambda x: len(x['description']), reverse=True)
        for i, issue in enumerate(sorted_issues[:3]):
            print(f"\n{i+1}. 【{issue['category']}】{issue['title']}")
            print(f"   问题描述: {issue['description'][:200]}{'...' if len(issue['description']) > 200 else ''}")
            print(f"   严重程度: {issue['severity']}")
            print(f"   描述长度: {len(issue['description'])} 字符")
        
        # 验证改进效果
        print(f"\n✅ 改进效果验证:")
        
        # 质量标准
        standards = [
            ("详细描述比例 ≥ 30%", quality['detailed_ratio'] >= 30),
            ("技术细节覆盖率 ≥ 40%", quality['technical_ratio'] >= 40),
            ("平均描述长度 ≥ 60字符", quality['avg_length'] >= 60)
        ]
        
        passed_count = sum(1 for _, passed in standards if passed)
        
        for standard, passed in standards:
            status = "✅" if passed else "❌"
            print(f"   {status} {standard}")
        
        print(f"\n📈 总体改进效果: {passed_count}/{len(standards)} 项标准通过")
        
        if passed_count >= 2:
            print("🎉 问题描述详细程度显著改进！")
        else:
            print("⚠️  问题描述详细程度仍需进一步改进")
        
        print(f"\n📄 详细报告已生成: {result['report_path']}")
        
    finally:
        # 清理临时文件
        if os.path.exists(test_file):
            os.unlink(test_file)
            print(f"🗑️  清理临时文件: {test_file}")

if __name__ == "__main__":
    main()
