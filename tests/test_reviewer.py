"""
AI需求文档评审系统测试模块
"""
import os
import sys
import unittest
import tempfile
import json
from unittest.mock import patch, MagicMock

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from ai_requirement_reviewer import AIRequirementReviewer
from document_parser import DocumentParser, DocumentSection, RequirementItem
from analysis_engine import AnalysisEngine, AnalysisIssue, AnalysisResult
from report_generator import ReportGenerator
from review_rules import ReviewRules, RuleCategory, Severity
from config import Config
import utils

class TestDocumentParser(unittest.TestCase):
    """文档解析器测试"""
    
    def setUp(self):
        self.parser = DocumentParser()
    
    def test_categorize_requirement(self):
        """测试需求分类功能"""
        # 测试功能需求
        functional_req = "用户可以登录系统"
        category = self.parser._categorize_requirement("功能需求", functional_req)
        self.assertEqual(category, "functional")
        
        # 测试非功能需求
        non_functional_req = "系统响应时间应小于2秒"
        category = self.parser._categorize_requirement("性能需求", non_functional_req)
        self.assertEqual(category, "non_functional")
    
    def test_is_heading(self):
        """测试标题识别功能"""
        # 创建模拟段落对象
        mock_paragraph = MagicMock()
        mock_paragraph.style.name = "Heading 1"
        mock_paragraph.text = "1. 项目概述"
        
        # 由于需要真实的docx对象，这里只测试逻辑
        # 实际测试需要真实的docx文件
        pass
    
    def test_extract_version_from_content(self):
        """测试版本号提取"""
        # 模拟文档内容
        with patch.object(self.parser, '_get_full_text') as mock_get_text:
            mock_get_text.return_value = "系统需求规范 版本：V1.2"
            version = self.parser._extract_version_from_content()
            self.assertEqual(version, "V1.2")

class TestAnalysisEngine(unittest.TestCase):
    """分析引擎测试"""
    
    def setUp(self):
        self.engine = AnalysisEngine()
    
    def test_calculate_overall_score(self):
        """测试总体评分计算"""
        category_scores = {
            'completeness': 80,
            'clarity': 70,
            'consistency': 90,
            'feasibility': 85,
            'testability': 75,
            'maintainability': 80
        }
        
        overall_score = self.engine._calculate_overall_score(category_scores)
        self.assertIsInstance(overall_score, float)
        self.assertGreaterEqual(overall_score, 0)
        self.assertLessEqual(overall_score, 100)
    
    def test_analyze_completeness(self):
        """测试完整性分析"""
        # 模拟解析数据
        parsed_data = {
            'sections': [
                DocumentSection("项目概述", 1, "这是项目概述内容"),
                DocumentSection("功能需求", 1, "这是功能需求内容")
            ],
            'requirements': [
                RequirementItem("REQ_001", "登录功能", "用户应该能够登录系统")
            ]
        }
        
        issues, score = self.engine._analyze_completeness(parsed_data)
        
        self.assertIsInstance(issues, list)
        self.assertIsInstance(score, (int, float))
        self.assertGreaterEqual(score, 0)
        self.assertLessEqual(score, 100)
    
    def test_analyze_clarity(self):
        """测试清晰度分析"""
        parsed_data = {
            'raw_text': "系统可能需要适当的性能优化，相关功能应该尽量满足用户需求",
            'requirements': [
                RequirementItem("REQ_001", "性能要求", "系统应该快速响应")
            ]
        }
        
        issues, score = self.engine._analyze_clarity(parsed_data)
        
        self.assertIsInstance(issues, list)
        self.assertIsInstance(score, (int, float))
        # 由于包含歧义词汇，分数应该被扣分
        self.assertLess(score, 100)

class TestReviewRules(unittest.TestCase):
    """评审规则测试"""
    
    def setUp(self):
        self.rules = ReviewRules()
    
    def test_get_rules_by_category(self):
        """测试按类别获取规则"""
        completeness_rules = self.rules.get_rules_by_category(RuleCategory.COMPLETENESS)
        self.assertIsInstance(completeness_rules, list)
        self.assertGreater(len(completeness_rules), 0)
        
        for rule in completeness_rules:
            self.assertEqual(rule.category, RuleCategory.COMPLETENESS)
            self.assertTrue(rule.enabled)
    
    def test_get_rules_by_severity(self):
        """测试按严重程度获取规则"""
        critical_rules = self.rules.get_rules_by_severity(Severity.CRITICAL)
        self.assertIsInstance(critical_rules, list)
        
        for rule in critical_rules:
            self.assertEqual(rule.severity, Severity.CRITICAL)
    
    def test_get_rule_by_id(self):
        """测试按ID获取规则"""
        rule = self.rules.get_rule_by_id("COMP_001")
        self.assertIsNotNone(rule)
        self.assertEqual(rule.id, "COMP_001")
        
        # 测试不存在的规则
        with self.assertRaises(ValueError):
            self.rules.get_rule_by_id("NONEXISTENT")

class TestUtils(unittest.TestCase):
    """工具函数测试"""
    
    def test_calculate_text_similarity(self):
        """测试文本相似度计算"""
        text1 = "用户可以登录系统"
        text2 = "用户能够登录系统"
        
        similarity = utils.calculate_text_similarity(text1, text2)
        self.assertIsInstance(similarity, float)
        self.assertGreaterEqual(similarity, 0)
        self.assertLessEqual(similarity, 1)
        self.assertGreater(similarity, 0.5)  # 应该有较高相似度
    
    def test_extract_keywords(self):
        """测试关键词提取"""
        text = "用户管理系统需要提供用户注册、登录、信息管理等功能"
        keywords = utils.extract_keywords(text, top_k=5)
        
        self.assertIsInstance(keywords, list)
        self.assertLessEqual(len(keywords), 5)
        
        for keyword, freq in keywords:
            self.assertIsInstance(keyword, str)
            self.assertIsInstance(freq, int)
            self.assertGreater(freq, 0)
    
    def test_validate_requirement_format(self):
        """测试需求格式验证"""
        # 测试有效需求
        valid_req = "用户应该能够通过用户名和密码登录系统"
        result = utils.validate_requirement_format(valid_req)
        self.assertTrue(result["is_valid"])
        
        # 测试无效需求（过短）
        invalid_req = "登录"
        result = utils.validate_requirement_format(invalid_req)
        self.assertFalse(result["is_valid"])
        self.assertIn("需求描述过短", result["issues"])
    
    def test_check_requirement_completeness(self):
        """测试需求完整性检查"""
        complete_req = "当用户输入正确的用户名和密码时，系统应该允许用户登录并显示主界面"
        result = utils.check_requirement_completeness(complete_req)
        
        self.assertIsInstance(result["completeness_score"], int)
        self.assertGreaterEqual(result["completeness_score"], 0)
        self.assertLessEqual(result["completeness_score"], 100)
    
    def test_calculate_readability_score(self):
        """测试可读性分数计算"""
        text = "这是一个简单的测试文本。它包含多个句子。每个句子都很短。"
        score = utils.calculate_readability_score(text)
        
        self.assertIsInstance(score, float)
        self.assertGreaterEqual(score, 0)
        self.assertLessEqual(score, 100)

class TestAIRequirementReviewer(unittest.TestCase):
    """AI需求评审器测试"""
    
    def setUp(self):
        self.reviewer = AIRequirementReviewer()
    
    def test_validate_file(self):
        """测试文件验证"""
        # 测试不存在的文件
        self.assertFalse(self.reviewer._validate_file("nonexistent.docx"))
        
        # 测试错误格式
        with tempfile.NamedTemporaryFile(suffix=".txt", delete=False) as tmp:
            tmp.write(b"test content")
            tmp_path = tmp.name
        
        try:
            self.assertFalse(self.reviewer._validate_file(tmp_path))
        finally:
            os.unlink(tmp_path)
    
    def test_get_available_rules(self):
        """测试获取可用规则"""
        rules_info = self.reviewer.get_available_rules()
        
        self.assertIn("total_rules", rules_info)
        self.assertIn("categories", rules_info)
        self.assertIn("severity_levels", rules_info)
        
        self.assertIsInstance(rules_info["total_rules"], int)
        self.assertGreater(rules_info["total_rules"], 0)
    
    def test_configure_rules(self):
        """测试规则配置"""
        # 获取第一个规则ID
        first_rule = self.reviewer.analyzer.rules.rules[0]
        original_state = first_rule.enabled
        
        # 切换状态
        self.reviewer.configure_rules({first_rule.id: not original_state})
        self.assertEqual(first_rule.enabled, not original_state)
        
        # 恢复原状态
        self.reviewer.configure_rules({first_rule.id: original_state})
        self.assertEqual(first_rule.enabled, original_state)

class TestIntegration(unittest.TestCase):
    """集成测试"""
    
    def setUp(self):
        self.reviewer = AIRequirementReviewer()
    
    def test_review_workflow_with_mock_data(self):
        """测试完整的评审工作流程（使用模拟数据）"""
        # 由于需要真实的docx文件，这里测试各组件的集成
        
        # 测试解析器初始化
        self.assertIsNotNone(self.reviewer.parser)
        
        # 测试分析器初始化
        self.assertIsNotNone(self.reviewer.analyzer)
        
        # 测试报告生成器初始化
        self.assertIsNotNone(self.reviewer.reporter)
        
        # 测试配置加载
        self.assertIsNotNone(self.reviewer.config)

def create_test_docx():
    """创建测试用的DOCX文件"""
    try:
        from docx import Document
        
        doc = Document()
        doc.add_heading('测试需求文档', 0)
        
        doc.add_heading('1. 项目概述', level=1)
        doc.add_paragraph('这是一个测试项目，用于验证AI需求评审系统的功能。')
        
        doc.add_heading('2. 功能需求', level=1)
        doc.add_paragraph('2.1 用户应该能够登录系统')
        doc.add_paragraph('2.2 系统必须提供数据查询功能')
        doc.add_paragraph('2.3 管理员可以管理用户账户')
        
        doc.add_heading('3. 非功能需求', level=1)
        doc.add_paragraph('3.1 系统响应时间应小于2秒')
        doc.add_paragraph('3.2 系统应支持100个并发用户')
        
        test_file = os.path.join(tempfile.gettempdir(), 'test_requirement.docx')
        doc.save(test_file)
        return test_file
    except ImportError:
        return None

class TestWithRealDocument(unittest.TestCase):
    """使用真实文档的测试"""
    
    @classmethod
    def setUpClass(cls):
        cls.test_file = create_test_docx()
        cls.reviewer = AIRequirementReviewer()
    
    @classmethod
    def tearDownClass(cls):
        if cls.test_file and os.path.exists(cls.test_file):
            os.unlink(cls.test_file)
    
    def test_full_review_process(self):
        """测试完整的评审流程"""
        if not self.test_file:
            self.skipTest("无法创建测试文档")
        
        result = self.reviewer.review_document(self.test_file)
        
        # 验证结果结构
        self.assertIn("success", result)
        if result["success"]:
            self.assertIn("overall_score", result)
            self.assertIn("category_scores", result)
            self.assertIn("total_issues", result)
            self.assertIn("report_path", result)
            
            # 验证评分范围
            self.assertGreaterEqual(result["overall_score"], 0)
            self.assertLessEqual(result["overall_score"], 100)
            
            # 验证报告文件存在
            self.assertTrue(os.path.exists(result["report_path"]))

def run_comprehensive_test():
    """运行综合测试"""
    print("开始运行AI需求文档评审系统综合测试...")
    
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加所有测试类
    test_classes = [
        TestDocumentParser,
        TestAnalysisEngine,
        TestReviewRules,
        TestUtils,
        TestAIRequirementReviewer,
        TestIntegration,
        TestWithRealDocument
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出测试结果摘要
    print(f"\n{'='*50}")
    print("测试结果摘要:")
    print(f"运行测试数: {result.testsRun}")
    print(f"失败数: {len(result.failures)}")
    print(f"错误数: {len(result.errors)}")
    print(f"跳过数: {len(result.skipped)}")
    
    if result.failures:
        print("\n失败的测试:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback.split('AssertionError:')[-1].strip()}")
    
    if result.errors:
        print("\n错误的测试:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback.split('Exception:')[-1].strip()}")
    
    success_rate = (result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100
    print(f"\n测试成功率: {success_rate:.1f}%")
    
    return result.wasSuccessful()

if __name__ == "__main__":
    # 运行综合测试
    success = run_comprehensive_test()
    
    if success:
        print("\n🎉 所有测试通过！系统可以正常使用。")
    else:
        print("\n❌ 部分测试失败，请检查系统实现。")
    
    sys.exit(0 if success else 1)
