"""
评审导向的需求分析演示 - 重点关注条件问题和逻辑漏洞
"""
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from analysis_engine import AnalysisEngine
from requirement_reviewer import RequirementReviewer
from report_generator import ReportGenerator
from document_parser import DocumentParser, DocumentMetadata

def demonstrate_review_oriented_analysis():
    """演示评审导向的分析功能"""
    print("🎯 评审导向的需求分析演示")
    print("=" * 80)
    print("重点：挖掘需求中的条件问题、逻辑漏洞和潜在风险")
    print("目标：为需求评审会议提供具体的讨论问题和关注点")
    print()
    
    # 选择文档进行分析
    available_docs = [
        "SYS-050-AHT RISD系统需求规范-V0.4-20250725.docx",
        "SYS-050-T9M-SIDP系统需求规范-V1.3-20250716.docx"
    ]
    
    selected_doc = None
    for doc in available_docs:
        if os.path.exists(doc):
            selected_doc = doc
            break
    
    if not selected_doc:
        print("❌ 未找到可用的需求文档")
        return
    
    print(f"📄 分析文档: {selected_doc}")
    print()
    
    # 执行评审导向分析
    print("🔍 执行评审导向分析...")
    engine = AnalysisEngine()
    analysis_result = engine.analyze_document(selected_doc)
    
    print("✅ 分析完成")
    print()
    
    # 显示分析重点
    print("📊 分析结果概览:")
    print(f"  • 发现问题总数: {len(analysis_result.issues)}")
    print(f"  • 生成评审问题: {len(analysis_result.review_questions) if analysis_result.review_questions else 0} 个")
    print(f"  • AI深度洞察: {len(analysis_result.ai_insights.get('test_risks', []))} 个风险点")
    print()
    
    # 重点展示评审问题
    if analysis_result.review_questions:
        print("❓ 需求评审问题清单 (重点关注):")
        print("=" * 60)
        
        # 按类别分组
        questions_by_category = {}
        for question in analysis_result.review_questions:
            if question.startswith('[') and ']' in question:
                category_end = question.find(']')
                category = question[1:category_end]
                content = question[category_end + 1:].strip()
            else:
                category = "其他"
                content = question
            
            if category not in questions_by_category:
                questions_by_category[category] = []
            questions_by_category[category].append(content)
        
        # 按重要性排序类别
        priority_order = ['状态转换', '触发条件', '功能优先级', '信号接口', '时序逻辑', '边界条件', '用户场景']
        
        for category in priority_order:
            if category in questions_by_category:
                questions = questions_by_category[category]
                print(f"\n🔸 {category}问题 ({len(questions)}个):")
                for i, question in enumerate(questions, 1):
                    if ' - ' in question:
                        q_part, concern_part = question.split(' - ', 1)
                        print(f"  {i}. {q_part}")
                        print(f"     💭 {concern_part}")
                    else:
                        print(f"  {i}. {question}")
                print()
    
    # 显示AI深度洞察
    ai_insights = analysis_result.ai_insights
    if ai_insights and ai_insights.get('test_risks'):
        print("🤖 AI深度洞察 (关键担忧):")
        print("=" * 60)
        
        test_risks = ai_insights.get('test_risks', [])
        for i, risk in enumerate(test_risks[:8], 1):  # 显示前8个最重要的
            print(f"{i}. {risk}")
        
        if len(test_risks) > 8:
            print(f"... 还有 {len(test_risks) - 8} 个风险点")
        print()
    
    # 显示汽车电子系统特有问题
    automotive_issues = [issue for issue in analysis_result.issues 
                        if issue.category.value in ['state_management', 'signal_interface', 
                                                   'boundary_conditions', 'function_conflicts', 
                                                   'timing_logic', 'error_handling']]
    
    if automotive_issues:
        print("🚗 汽车电子系统特有问题:")
        print("=" * 60)
        
        # 按严重程度分组
        critical_issues = [i for i in automotive_issues if i.severity.value == 'critical']
        high_issues = [i for i in automotive_issues if i.severity.value == 'high']
        
        if critical_issues:
            print("🔴 严重问题:")
            for issue in critical_issues:
                print(f"  • {issue.title}")
                print(f"    {issue.description}")
                print()
        
        if high_issues:
            print("🟠 高优先级问题:")
            for issue in high_issues[:5]:  # 显示前5个
                print(f"  • {issue.title}")
                print(f"    {issue.description}")
            
            if len(high_issues) > 5:
                print(f"  ... 还有 {len(high_issues) - 5} 个高优先级问题")
            print()
    
    # 生成评审报告
    print("📝 生成评审导向报告...")
    report_generator = ReportGenerator()
    
    # 解析文档元数据
    parser = DocumentParser()
    parsed_data = parser.parse_document(selected_doc)
    metadata = parsed_data.get('metadata')
    
    if not metadata:
        metadata = DocumentMetadata(
            title=os.path.basename(selected_doc),
            version="评审版本",
            author="需求分析师",
            total_words=0,
            total_requirements=len(parsed_data.get('requirements', []))
        )
    
    report_path = report_generator.generate_report(analysis_result, metadata, "html")
    print(f"✅ 评审报告已生成: {report_path}")
    print()
    
    # 生成独立的评审问题清单
    if analysis_result.review_questions:
        reviewer = RequirementReviewer()
        # 重新生成详细的评审问题
        review_questions_obj = reviewer.generate_review_questions(parsed_data)
        review_report = reviewer.generate_review_report(review_questions_obj)
        
        review_file = "需求评审问题清单_详细版.md"
        with open(review_file, 'w', encoding='utf-8') as f:
            f.write(review_report)
        print(f"✅ 详细评审问题清单已生成: {review_file}")
        print()
    
    # 提供使用建议
    print("💡 评审会议使用建议:")
    print("=" * 60)
    suggestions = [
        "📋 将评审问题清单打印出来，作为会议讨论的检查清单",
        "🎯 按问题优先级顺序讨论，确保关键问题得到解决",
        "📝 对每个问题的澄清结果进行记录，更新需求文档",
        "🧪 基于发现的问题设计针对性的测试用例",
        "🔄 评审后重新运行分析，验证问题是否得到解决",
        "📊 将HTML报告分享给所有评审参与者，提前了解问题"
    ]
    
    for suggestion in suggestions:
        print(f"  {suggestion}")
    print()
    
    print("🎉 评审导向分析演示完成!")
    print("=" * 80)
    
    return analysis_result

def show_analysis_comparison():
    """展示传统分析vs评审导向分析的对比"""
    print("\n📊 传统分析 vs 评审导向分析对比:")
    print("=" * 80)
    
    comparison = [
        ("关注重点", "评分和统计", "条件问题和逻辑漏洞"),
        ("输出形式", "问题列表和评分", "具体的评审问题和担忧"),
        ("使用场景", "质量评估", "需求评审会议"),
        ("问题深度", "表面问题识别", "深层逻辑分析"),
        ("实用性", "了解文档质量", "直接指导评审讨论"),
        ("AI角色", "质量检查员", "资深需求分析师"),
        ("问题表述", "技术性描述", "评审会议语言"),
        ("行动指导", "改进建议", "具体讨论问题")
    ]
    
    print(f"{'维度':<12} {'传统分析':<20} {'评审导向分析'}")
    print("-" * 80)
    for dimension, traditional, review_oriented in comparison:
        print(f"{dimension:<12} {traditional:<20} {review_oriented}")
    
    print()
    print("💼 评审导向分析的优势:")
    advantages = [
        "🎯 直接服务于需求评审会议，提供可操作的讨论问题",
        "🔍 深度挖掘条件逻辑漏洞，避免开发阶段的理解偏差",
        "💭 以分析师的视角提出疑问和担忧，更贴近实际评审场景",
        "📋 生成结构化的评审检查清单，提高评审效率",
        "🚨 重点关注影响系统安全和用户体验的关键问题",
        "🔄 促进需求文档的持续改进和完善"
    ]
    
    for advantage in advantages:
        print(f"  {advantage}")

def main():
    """主函数"""
    # 执行评审导向分析演示
    result = demonstrate_review_oriented_analysis()
    
    # 显示对比分析
    show_analysis_comparison()
    
    print(f"\n📈 本次分析结果:")
    print(f"  • 总问题数: {len(result.issues) if result else 0}")
    print(f"  • 评审问题: {len(result.review_questions) if result and result.review_questions else 0}")
    print(f"  • AI洞察: {len(result.ai_insights.get('test_risks', [])) if result else 0}")
    
    print(f"\n🎯 核心价值:")
    print(f"  不再只是评分，而是真正帮助团队发现和解决需求中的问题！")

if __name__ == "__main__":
    main()
